import React, { useEffect, useRef } from 'react'
import ReactDOMServer from 'react-dom/server'
import FormPDFLayout from './FormPDFLayout'
import html2pdf from 'html2pdf.js'
import jsPDF from 'jspdf'
import { createPortal } from 'react-dom'
import { PDFDownloadLink, PDFViewer, usePDF } from '@react-pdf/renderer'
import ReactPDF from '@react-pdf/renderer'
import { Previewer } from 'pagedjs'
import html2canvas from 'html2canvas'

const Hello = ({
  weather,
  imageBase64,
  formattedResponses,
  name,
  oppData,
  createdBy,
}: any) => {
  const pdfRoot = document.getElementById('pdf-root')

  // const { toPDF, targetRef } = usePDF({
  //   method: 'save',
  //   filename: 'multipage-example.pdf',
  //   page: { margin: Margin.MEDIUM },
  // })

  const targetRef = useRef(null)

  console.log('formattedResponses===[log]===>', formattedResponses)
  // useEffect(() => {
  //   ;(async () => {
  //     try {
  //       // 1️⃣ Convert HTML to PDF Blob
  //       const htmlContent = ReactDOMServer.renderToString(
  //         <FormPDFLayout
  //           weather={weather?.data}
  //           imageBase64={imageBase64}
  //           formattedResponses={formattedResponses}
  //           // name={formDataById?.name}
  //           oppData={oppData}
  //           // createdBy={currentMember?.name}
  //         />
  //       )

  //       const sections = document.querySelectorAll('.field-group-col')
  //       let currentHeight = 0
  //       const usableHeightPx = 1000 // calculate from jsPDF settings

  //       sections.forEach(section => {
  //         const height = section.offsetHeight

  //         if (currentHeight + height > usableHeightPx) {
  //           section.classList.add('page-break-before')
  //           currentHeight = height
  //         } else {
  //           currentHeight += height
  //         }
  //       })

  //       const element = document.createElement('div')
  //       element.innerHTML = `
  //     <style>
  //     * { word-wrap: break-word; }
  //     table, div { page-break-inside: avoid; }
  //     p { page-break-inside: avoid; word-break: break-word; }

  //     /* Ensure sections don’t split */
  //     .avoid-break {
  //       page-break-before: always;
  //       page-break-inside: avoid;
  //     }
  //   </style>
  //   ${htmlContent}
  // `

  //       element.innerHTML = htmlContent

  //       const pdfBlob = await html2pdf()
  //         .set({
  //           margin: 8.5, // Explicit margins [top, right, bottom, left]
  //           filename: 'document.pdf',
  //           image: { type: 'jpeg', quality: 1.0 },
  //           html2canvas: {
  //             scale: 2, // Reduced from 2 to prevent scaling overflow
  //             useCORS: true,
  //             imageTimeout: 0,
  //           },
  //           jsPDF: {
  //             unit: 'mm',
  //             format: 'letter',
  //             orientation: 'portrait',
  //           },
  //           pagebreak: { mode: ['avoid', 'css'] },
  //           // margin: 10,
  //           // filename: 'report.pdf',
  //           // image: { type: 'jpeg', quality: 0.98 },
  //           // html2canvas: { scale: 2, useCORS: true },
  //           // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  //           // pagebreak: { mode: ['avoid-all', 'css'] },
  //         })
  //         .from(htmlContent)
  //         .outputPdf('blob')

  //       console.log('TEST===[log]===>', {
  //         url: URL.createObjectURL(pdfBlob),
  //       })
  //     } catch (error) {
  //       console.log('TEST===[error]===>', error)
  //     }
  //   })()
  // }, [])

  // useEffect(() => {
  //   ;(async () => {
  //     const element = targetRef.current
  //     const sections = element.querySelectorAll(
  //       '.content > .field-group .field-group-col'
  //     )
  //     let currentHeight = 0
  //     const usableHeightPx = 1056 // calculate from jsPDF settings
  //     console.log('sections===[log]===>', sections)
  //     sections.forEach(section => {
  //       const height = section.offsetHeight
  //       console.log('height===[log]===>', {
  //         height,
  //         currentHeight,
  //         totoal: currentHeight + height,
  //         usableHeightPx,
  //       })
  //       if (currentHeight + height > usableHeightPx) {
  //         section.classList.add('page-break-before')
  //         currentHeight = height
  //       } else {
  //         currentHeight += height
  //       }
  //     })

  //     console.log('element===[log]===>', element)

  //     const pdfBlob = await html2pdf()
  //       .set({
  //         margin: 8.5, // Explicit margins [top, right, bottom, left]
  //         filename: 'document.pdf',
  //         image: { type: 'jpeg', quality: 1.0 },
  //         html2canvas: {
  //           scale: 2, // Reduced from 2 to prevent scaling overflow
  //           useCORS: true,
  //           imageTimeout: 0,
  //         },
  //         jsPDF: {
  //           unit: 'mm',
  //           format: 'letter',
  //           orientation: 'portrait',
  //         },
  //         pagebreak: { mode: ['avoid', 'css'] },
  //         // margin: 10,
  //         // filename: 'report.pdf',
  //         // image: { type: 'jpeg', quality: 0.98 },
  //         // html2canvas: { scale: 2, useCORS: true },
  //         // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  //         // pagebreak: { mode: ['avoid-all', 'css'] },
  //       })
  //       .from(element)
  //       .outputPdf('blob')
  //     console.log('TEST===[log]===>', {
  //       url: URL.createObjectURL(pdfBlob),
  //     })
  //   })()
  // }, [])

  // Function to calculate page dimensions in pixels
  const calculatePageDimensions = () => {
    // Letter size: 8.5" x 11" (216mm x 279mm)
    // With 8.5mm margins on all sides
    const pageWidthMm = 216
    const pageHeightMm = 279
    const marginMm = 8.5

    // Convert to pixels (assuming 96 DPI)
    const mmToPx = 96 / 25.4
    const usableHeightPx = (pageHeightMm - 2 * marginMm) * mmToPx
    const usableWidthPx = (pageWidthMm - 2 * marginMm) * mmToPx

    return { usableHeightPx, usableWidthPx }
  }

  // Debug function to add visual page break indicators (for development)
  const addPageBreakIndicators = (element: HTMLElement) => {
    const pageBreaks = element.querySelectorAll('.page-break-before')
    pageBreaks.forEach((breakElement, index) => {
      const indicator = document.createElement('div')
      indicator.style.cssText = `
        background: red;
        color: white;
        padding: 5px;
        margin: 5px 0;
        font-size: 12px;
        text-align: center;
        border: 2px dashed red;
      `
      indicator.textContent = `--- PAGE BREAK ${index + 2} ---`
      breakElement.parentNode?.insertBefore(indicator, breakElement)
    })
  }

  // Function to handle page breaks for content sections
  const handlePageBreaks = (element: HTMLElement) => {
    const { usableHeightPx } = calculatePageDimensions()

    // Get all field groups and sections that should be kept together
    const sections = element.querySelectorAll(
      '.field-group, .field-group-col, .section-title, .dual-column'
    )

    let currentPageHeight = 0
    let pageNumber = 1

    // Account for header height (logo + inspection header)
    const header = element.querySelector('.flex')
    if (header) {
      currentPageHeight += (header as HTMLElement).offsetHeight + 20 // Add some padding
    }

    sections.forEach((section, index) => {
      const sectionElement = section as HTMLElement
      let sectionHeight = sectionElement.offsetHeight

      // Special handling for sections with images - they might need more space
      const hasImages = sectionElement.querySelector('img')
      if (hasImages) {
        // Add extra buffer for image sections to prevent awkward breaks
        sectionHeight += 20
      }

      // Check if this section would overflow the current page
      if (
        currentPageHeight + sectionHeight > usableHeightPx &&
        currentPageHeight > 0
      ) {
        // Add page break before this section
        sectionElement.style.setProperty('break-before', 'page', 'important')
        sectionElement.classList.add('page-break-before')

        // Reset height counter for new page
        currentPageHeight = sectionHeight
        pageNumber++

        console.log(
          `Page break added before section ${index} (${sectionElement.className}), starting page ${pageNumber}`
        )
      } else {
        // Remove any existing page breaks
        sectionElement.style.removeProperty('break-before')
        sectionElement.classList.remove('page-break-before')

        // Add to current page height
        currentPageHeight += sectionHeight
      }

      // Special handling for image groups to avoid splitting
      const imageGroups = sectionElement.querySelectorAll('.grid-layout > div')
      imageGroups.forEach(group => {
        const groupElement = group as HTMLElement
        groupElement.classList.add('no-break')
        groupElement.style.setProperty('break-inside', 'avoid', 'important')
      })

      // Ensure all images have proper break protection
      const images = sectionElement.querySelectorAll('img')
      images.forEach(img => {
        const imgElement = img as HTMLElement
        imgElement.style.setProperty('break-inside', 'avoid', 'important')
        imgElement.style.setProperty('page-break-inside', 'avoid', 'important')
      })
    })

    console.log(`Total pages estimated: ${pageNumber}`)
    console.log(`Usable page height: ${usableHeightPx}px`)
  }

  useEffect(() => {
    if (!targetRef.current) return

    const images = Array.from(
      (targetRef.current as HTMLElement).querySelectorAll('img')
    )
    let loaded = 0

    const handleLoad = () => {
      loaded += 1
      if (loaded === images.length) {
        setTimeout(() => {
          ;(async () => {
            const element = targetRef.current
            if (!element) return

            // Apply page break logic
            handlePageBreaks(element)

            // Uncomment the line below to see visual page break indicators during development
            // addPageBreakIndicators(element)

            const pdfBlob = await html2pdf()
              .set({
                margin: 8.5,
                filename: 'inspection-report.pdf',
                image: { type: 'jpeg', quality: 1.0 },
                html2canvas: {
                  scale: 2,
                  useCORS: true,
                  imageTimeout: 0,
                  allowTaint: true,
                  logging: false,
                  height: null,
                  width: null,
                },
                jsPDF: {
                  unit: 'mm',
                  format: 'letter',
                  orientation: 'portrait',
                  compress: true,
                },
                pagebreak: {
                  mode: ['css', 'avoid-all'],
                  avoid: [
                    '.no-break',
                    '.field-group',
                    '.field-group-col',
                    '.grid-layout',
                    'img',
                  ],
                  before: ['.page-break-before'],
                },
              })
              .from(element)
              .outputPdf('blob')

            console.log('PDF Generated:', {
              url: URL.createObjectURL(pdfBlob),
            })
          })()
        }, 1000)
      }
    }

    // Handle image loading
    if (images.length === 0) {
      handleLoad() // No images, proceed immediately
    } else {
      images.forEach((img: any) => {
        if (img.complete) {
          handleLoad() // already cached/loaded
        } else {
          img.addEventListener('load', handleLoad)
          img.addEventListener('error', handleLoad) // still count errors
        }
      })
    }

    // cleanup listeners
    return () => {
      images.forEach((img: any) => {
        img.removeEventListener('load', handleLoad)
        img.removeEventListener('error', handleLoad)
      })
    }
  }, [targetRef])

  return (
    <div>
      {createPortal(
        <div ref={targetRef}>
          <FormPDFLayout
            weather={weather?.data}
            imageBase64={imageBase64}
            formattedResponses={formattedResponses}
            name={name || 'Inspection Report'}
            oppData={oppData}
            createdBy={createdBy || 'Inspector'}
          />
        </div>,
        pdfRoot!
      )}
    </div>
  )
}

export default Hello
