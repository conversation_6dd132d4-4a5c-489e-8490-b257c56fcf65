import React from 'react'
import './App.css'

// Demo component to show how page breaks work
const PageBreakDemo = () => {
  const generateTestData = () => {
    const testFields = []
    
    // Add header
    testFields.push({
      type: 'header',
      label: 'Inspection Report Demo',
      subtype: 'h2'
    })
    
    // Add some regular fields
    for (let i = 1; i <= 15; i++) {
      testFields.push({
        type: 'text',
        label: `Field ${i}`,
        value: `This is test content for field ${i}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`
      })
    }
    
    // Add a section with images
    testFields.push({
      type: 'file',
      label: 'Test Images',
      value: [
        { url: 'https://via.placeholder.com/200x150/FF0000/FFFFFF?text=Image+1', mimetype: 'image/jpeg' },
        { url: 'https://via.placeholder.com/200x150/00FF00/FFFFFF?text=Image+2', mimetype: 'image/jpeg' },
        { url: 'https://via.placeholder.com/200x150/0000FF/FFFFFF?text=Image+3', mimetype: 'image/jpeg' },
      ]
    })
    
    // Add more fields after images
    for (let i = 16; i <= 25; i++) {
      testFields.push({
        type: 'text',
        label: `Field ${i}`,
        value: `This is test content for field ${i}. This content should demonstrate how page breaks work when there's insufficient space.`
      })
    }
    
    return testFields
  }

  const testData = generateTestData()

  return (
    <div className='inspection-report'>
      <div className='flex'>
        <div className='inspection-header'>
          <h2>Page Break Demo Report</h2>
          <p>Completed: {new Date().toLocaleDateString()}</p>
          <p>Prepared by: Demo Inspector</p>
        </div>
        <div>
          <div style={{ width: '300px', height: '100px', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            LOGO PLACEHOLDER
          </div>
        </div>
      </div>

      <div className='dual-column'>
        <div className='column'>
          <div className='dual-column-title'>Client:</div>
          <div className='client-group'>
            <div>John Doe</div>
            <div><EMAIL></div>
          </div>
        </div>
        <div className='column'>
          <div className='dual-column-title'>Project Address:</div>
          <div className='client-group'>
            <div>123 Main Street</div>
            <div>Anytown, ST 12345</div>
          </div>
        </div>
      </div>

      <div>
        {testData.map((field, index) => {
          switch (field.type) {
            case 'header':
              return (
                <div key={index} className='section-title no-break'>
                  {React.createElement(
                    field.subtype || 'h3',
                    { className: 'header-field' },
                    field.label
                  )}
                </div>
              )
            
            case 'file':
              return (
                <div key={index} className='field-group-col no-break'>
                  <span className='field-label'>{field.label}</span>
                  <div className='grid-layout'>
                    {field.value.map((file: any, idx: number) => (
                      <div key={idx} className='no-break'>
                        <img
                          src={file.url}
                          alt={`Test ${idx}`}
                          className='uploaded-image'
                          crossOrigin='anonymous'
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )
            
            default:
              return (
                <div key={index} className='field-group'>
                  <span className='field-label'>{field.label}</span>
                  <div className='field-value'>{field.value}</div>
                </div>
              )
          }
        })}
      </div>
    </div>
  )
}

export default PageBreakDemo
