import React from 'react'
import Logo from './assets/nhr.png'
import './App.css'

// import { dayjsFormat, formatPhoneNumber } from '../../../shared/helpers/util'
// import { generateWeatherString } from './constant'
// import { I_Opportunity } from '../../opportunity/Opportunity'

const FormPDFLayout = ({
  weather,
  imageBase64,
  formattedResponses,
  name,
  oppData,
  createdBy,
}: {
  weather: any
  imageBase64: any
  formattedResponses: any[]
  name: string
  createdBy: string
  oppData: any
}) => {
  // const mapKey = getConfig()?.mapsAPIKey!

  // const latitude = 22.7146867
  // const longitude = 75.8968164
  // const zoom = 17
  // const satelliteViewEmbedUrl = `https://www.google.com/maps/embed/v1/view?key=${mapKey}&center=${latitude},${longitude}&zoom=${zoom}&maptype=satellite`
  console.log({ formattedResponses })
  return (
    <div className='inspection-report'>
      <div className='flex'>
        <div className='inspection-header'>
          <h2>{name}</h2>
          <p>Completed:</p>
          <p>Prepared by: {createdBy}</p>
        </div>

        <div>
          <img src={Logo} alt='NHR logo' className='logo' />
        </div>
      </div>

      {oppData ? (
        <div className='dual-column'>
          <div className='column'>
            <div className='dual-column-title'>Client:</div>
            <div className='client-group'>
              <div>{oppData?.contact?.fullName}</div>
              {/* <div>{oppData?.contact?.street}</div>
              <div>
                {oppData?.contact?.city}, {oppData?.contact?.state} {oppData?.contact?.zip}
              </div> */}
              <div>""</div>
              <div>{oppData?.contact?.email}</div>
            </div>
          </div>
          <div className='column'>
            <div className='dual-column-title'>Project Address:</div>
            <div className='client-group'>
              {/* <div>
                {oppData?.client?.contacts?.[0]?.firstName} {oppData?.client?.contacts?.[0]?.lastName}
              </div> */}
              <div>{oppData?.street}</div>
              <div>
                {oppData?.city}, {oppData?.state} {oppData?.zip}
              </div>
              {/* <div>{formatPhoneNumber(oppData?.client?.phone, '')}</div>
              <div>{oppData?.client?.email}</div> */}
            </div>
          </div>
        </div>
      ) : null}

      {/* <div className="field-group">
        <span className="field-label">Short Text with a long name</span>
        <div className="field-value">
          Lorem ipsum dolor, sit amet consectetur adipisicing elit. Est quisquam eaque laudantium ipsam voluptates,
          sequi ut aliquid assumenda voluptatum at. Doloribus omnis mollitia aperiam, reprehenderit illum consequatur
          beatae fugit similique?
        </div>
      </div> */}

      <div>
        {formattedResponses
          ?.sort((a: any, b: any) => a?.order - b?.order) // Sort fields by `order`
          ?.map((field: any, index: number) => {
            let fieldValue
            console.log({ field }, 'fdfgdsfgdfg')
            switch (field.type) {
              case 'header':
                return (
                  <div className='section-title no-break'>
                    {field.type === 'header' ? (
                      React.createElement(
                        field.subtype || 'h3', // Default to h3 if subtype missing
                        { className: 'header-field' },
                        field.label
                      )
                    ) : (
                      <div className='default-field'>{field.label}</div>
                    )}
                  </div>

                  // <div className="section-title no-break">
                  //   {field?.label}
                  // </div>
                )
                break
              case 'paragraph':
                return (
                  <div
                    className='field-value'
                    dangerouslySetInnerHTML={{
                      __html: field?.label,
                    }}
                  ></div>
                )
                break
              case 'date': {
                let displayDate = 'N/A'

                if (field.value) {
                  const rawValue = field.value

                  switch (field.subtype) {
                    case 'datetime-local': {
                      const date = new Date(rawValue)
                      displayDate = date.toLocaleString(undefined, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true,
                      })
                      break
                    }

                    case 'time': {
                      const [hours, minutes] = rawValue.split(':')
                      const date = new Date()
                      date.setHours(+hours)
                      date.setMinutes(+minutes)
                      displayDate = date.toLocaleTimeString(undefined, {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true,
                      })
                      break
                    }

                    case 'date':
                    default: {
                      const date = new Date(rawValue)
                      displayDate = date.toLocaleDateString(undefined, {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })
                      break
                    }
                  }
                }

                return (
                  <div className='field-group'>
                    <span className='field-label'>{field.label}</span>
                    <div className='field-value'>{displayDate}</div>
                  </div>
                )
              }

              case 'checkbox-group':
                fieldValue =
                  field?.values && field?.values?.length > 0
                    ? field?.value?.map((v: any, idx: number) => (
                        <div key={idx}>{v}</div>
                      ))
                    : 'N/A'

                break
              // case 'radio-group':
              //   fieldValue = field?.values?.find((v: any) => v.selected === true)?.label || 'N/A'
              //   break
              case 'weather':
                return (
                  <div className='field-group-col'>
                    <span className='field-label'>Weather</span>
                    <div className='field-value'></div>
                  </div>
                )
                break
              case 'location':
                return (
                  <div className='field-group-col no-break'>
                    <span className='field-label'>Location</span>
                    <div className='field-value'>
                      <>
                        {/* <iframe
                         width="400"
                         height="300"
                          src={satelliteViewEmbedUrl}
                          allowFullScreen
                          loading="lazy"
                        ></iframe> */}
                      </>
                      {imageBase64 ? (
                        <img
                          src={`${imageBase64}`}
                          alt='Satellite View'
                          width='250'
                          height='200'
                        />
                      ) : (
                        field?.value
                      )}
                    </div>
                  </div>
                )
                break
              case 'file':
                return (
                  <div className='field-group-col no-break'>
                    <span className='field-label'>{field.label}</span>
                    {/* <span className="field-label">{field.label || 'Uploaded Files'}</span> */}
                    <div className='grid-layout'>
                      {Array.isArray(field.value) && field.value.length > 0 ? (
                        <>
                          {/* First, display images in groups of 3 */}
                          {(() => {
                            const images = field.value.filter((file: any) =>
                              file.mimetype?.startsWith('image')
                            )
                            const chunked: any[][] = []
                            for (let i = 0; i < images.length; i += 3) {
                              chunked.push(images.slice(i, i + 3))
                            }

                            return chunked.map((group, groupIdx) => (
                              <div
                                className='no-break'
                                data-html2pdf-pagebreak='avoid'
                              >
                                {group.map((file, idx) => (
                                  <img
                                    key={idx}
                                    src={`${file?.url}?nocache=${Date.now()}`}
                                    alt={`Uploaded ${groupIdx * 3 + idx}`}
                                    className='uploaded-image'
                                    crossOrigin='anonymous'
                                  />
                                ))}
                              </div>
                            ))
                          })()}

                          {/* Then, display links for audio and video files below */}
                          <div className='file-links'>
                            {field.value
                              .filter(
                                (file: any) =>
                                  file.mimetype?.startsWith('audio') ||
                                  file.mimetype?.startsWith('video')
                              )
                              .map((file: any, idx: number) => (
                                <div key={idx} className='no-break'>
                                  {!field.accept ? (
                                    <label>
                                      {file.mimetype?.startsWith('audio')
                                        ? 'Audio:- '
                                        : 'Video:- '}
                                    </label>
                                  ) : null}
                                  <a
                                    href={file.url}
                                    target='_blank'
                                    rel='noopener noreferrer'
                                    className='file-link'
                                  >
                                    {file.url}
                                  </a>
                                </div>
                              ))}
                          </div>
                        </>
                      ) : (
                        <span className='no-image'>No files uploaded</span>
                      )}
                    </div>
                  </div>
                )
              default:
                fieldValue = field?.value ? field?.value : 'N/A'
              // fieldValue = field?.value ? (field?.subtype === 'password' ? '********' : field?.value) : 'N/A'
            }

            return (
              <div className='field-group' key={index}>
                <span className='field-label'>{field?.label}</span>
                <div className='field-value'>{fieldValue || '--'}</div>
              </div>
            )
          })}
      </div>
      {/* <a href="https://www.w3schools.com" download>
        Download PDF
      </a> */}
      {/* <div className="signature-line">Inspector Signature: ___________________________</div> */}
    </div>
  )
}

export default FormPDFLayout
