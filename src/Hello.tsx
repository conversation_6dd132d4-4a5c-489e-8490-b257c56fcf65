import React, { useEffect, useRef } from 'react'
import ReactDOMServer from 'react-dom/server'
import FormPDFLayout from './FormPDFLayout'
import html2pdf from 'html2pdf.js'
import jsPDF from 'jspdf'
import { createPortal } from 'react-dom'
import { PDFDownloadLink, PDFViewer, usePDF } from '@react-pdf/renderer'
import ReactPDF from '@react-pdf/renderer'
import { Previewer } from 'pagedjs'
import html2canvas from 'html2canvas'

const Hello = ({
  weather,
  imageBase64,
  formattedResponses,
  name,
  oppData,
  createdBy,
}: any) => {
  const pdfRoot = document.getElementById('pdf-root')

  // const { toPDF, targetRef } = usePDF({
  //   method: 'save',
  //   filename: 'multipage-example.pdf',
  //   page: { margin: Margin.MEDIUM },
  // })

  const targetRef = useRef(null)

  console.log('formattedResponses===[log]===>', formattedResponses)
  // useEffect(() => {
  //   ;(async () => {
  //     try {
  //       // 1️⃣ Convert HTML to PDF Blob
  //       const htmlContent = ReactDOMServer.renderToString(
  //         <FormPDFLayout
  //           weather={weather?.data}
  //           imageBase64={imageBase64}
  //           formattedResponses={formattedResponses}
  //           // name={formDataById?.name}
  //           oppData={oppData}
  //           // createdBy={currentMember?.name}
  //         />
  //       )

  //       const sections = document.querySelectorAll('.field-group-col')
  //       let currentHeight = 0
  //       const usableHeightPx = 1000 // calculate from jsPDF settings

  //       sections.forEach(section => {
  //         const height = section.offsetHeight

  //         if (currentHeight + height > usableHeightPx) {
  //           section.classList.add('page-break-before')
  //           currentHeight = height
  //         } else {
  //           currentHeight += height
  //         }
  //       })

  //       const element = document.createElement('div')
  //       element.innerHTML = `
  //     <style>
  //     * { word-wrap: break-word; }
  //     table, div { page-break-inside: avoid; }
  //     p { page-break-inside: avoid; word-break: break-word; }

  //     /* Ensure sections don’t split */
  //     .avoid-break {
  //       page-break-before: always;
  //       page-break-inside: avoid;
  //     }
  //   </style>
  //   ${htmlContent}
  // `

  //       element.innerHTML = htmlContent

  //       const pdfBlob = await html2pdf()
  //         .set({
  //           margin: 8.5, // Explicit margins [top, right, bottom, left]
  //           filename: 'document.pdf',
  //           image: { type: 'jpeg', quality: 1.0 },
  //           html2canvas: {
  //             scale: 2, // Reduced from 2 to prevent scaling overflow
  //             useCORS: true,
  //             imageTimeout: 0,
  //           },
  //           jsPDF: {
  //             unit: 'mm',
  //             format: 'letter',
  //             orientation: 'portrait',
  //           },
  //           pagebreak: { mode: ['avoid', 'css'] },
  //           // margin: 10,
  //           // filename: 'report.pdf',
  //           // image: { type: 'jpeg', quality: 0.98 },
  //           // html2canvas: { scale: 2, useCORS: true },
  //           // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  //           // pagebreak: { mode: ['avoid-all', 'css'] },
  //         })
  //         .from(htmlContent)
  //         .outputPdf('blob')

  //       console.log('TEST===[log]===>', {
  //         url: URL.createObjectURL(pdfBlob),
  //       })
  //     } catch (error) {
  //       console.log('TEST===[error]===>', error)
  //     }
  //   })()
  // }, [])

  // useEffect(() => {
  //   ;(async () => {
  //     const element = targetRef.current
  //     const sections = element.querySelectorAll(
  //       '.content > .field-group .field-group-col'
  //     )
  //     let currentHeight = 0
  //     const usableHeightPx = 1056 // calculate from jsPDF settings
  //     console.log('sections===[log]===>', sections)
  //     sections.forEach(section => {
  //       const height = section.offsetHeight
  //       console.log('height===[log]===>', {
  //         height,
  //         currentHeight,
  //         totoal: currentHeight + height,
  //         usableHeightPx,
  //       })
  //       if (currentHeight + height > usableHeightPx) {
  //         section.classList.add('page-break-before')
  //         currentHeight = height
  //       } else {
  //         currentHeight += height
  //       }
  //     })

  //     console.log('element===[log]===>', element)

  //     const pdfBlob = await html2pdf()
  //       .set({
  //         margin: 8.5, // Explicit margins [top, right, bottom, left]
  //         filename: 'document.pdf',
  //         image: { type: 'jpeg', quality: 1.0 },
  //         html2canvas: {
  //           scale: 2, // Reduced from 2 to prevent scaling overflow
  //           useCORS: true,
  //           imageTimeout: 0,
  //         },
  //         jsPDF: {
  //           unit: 'mm',
  //           format: 'letter',
  //           orientation: 'portrait',
  //         },
  //         pagebreak: { mode: ['avoid', 'css'] },
  //         // margin: 10,
  //         // filename: 'report.pdf',
  //         // image: { type: 'jpeg', quality: 0.98 },
  //         // html2canvas: { scale: 2, useCORS: true },
  //         // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  //         // pagebreak: { mode: ['avoid-all', 'css'] },
  //       })
  //       .from(element)
  //       .outputPdf('blob')
  //     console.log('TEST===[log]===>', {
  //       url: URL.createObjectURL(pdfBlob),
  //     })
  //   })()
  // }, [])

  useEffect(() => {
    if (!targetRef.current) return

    const images = Array.from(targetRef.current.querySelectorAll('img'))
    // if (images.length === 0) {
    //   toPDF()
    //   return
    // }

    let loaded = 0

    const handleLoad = () => {
      loaded += 1
      if (loaded === images.length) {
        setTimeout(() => {
          ;(async () => {
            const htmlContent = targetRef?.current

            console.log('htmlContent===[log]===>', htmlContent)

            // const reports = document.querySelectorAll('.inspection-report')

            // reports.forEach(report => {
            //   if (report.scrollHeight > report.clientHeight) {
            //     // Calculate overflow
            //     const overflow = report.scrollHeight - report.clientHeight

            //     // Create a clone for next page
            //     const clone = report.cloneNode(true)

            //     // Push clone to next page with padding
            //     clone.style.paddingTop = overflow + 'px'

            //     // Insert after current report
            //     report.after(clone)
            //   }
            // })

            const pdfBlob = await html2pdf()
              .set({
                margin: 8.5,
                filename: 'document.pdf',
                image: { type: 'jpeg', quality: 1.0 },
                html2canvas: { scale: 2, useCORS: true },
                jsPDF: {
                  unit: 'mm',
                  format: 'letter',
                  orientation: 'portrait',
                },
                pagebreak: {
                  mode: ['css', 'avoid-all'],
                  avoid: ['.no-break', 'img'],
                },
              })
              .from(htmlContent)
              .outputPdf('blob')

            console.log('TEST===[log]===>', {
              url: URL.createObjectURL(pdfBlob),
            })
          })()
        }, 1000)
      }
    }

    images.forEach((img: any) => {
      if (img.complete) {
        handleLoad() // already cached/loaded
      } else {
        img.addEventListener('load', handleLoad)
        img.addEventListener('error', handleLoad) // still count errors
      }
    })

    // cleanup listeners
    return () => {
      images.forEach((img: any) => {
        img.removeEventListener('load', handleLoad)
        img.removeEventListener('error', handleLoad)
      })
    }
  }, [targetRef])

  return (
    <div>
      {createPortal(
        <div ref={targetRef}>
          <FormPDFLayout
            weather={weather?.data}
            imageBase64={imageBase64}
            formattedResponses={formattedResponses}
            // name={formDataById?.name}
            oppData={oppData}
            // createdBy={currentMember?.name}
          />
        </div>,
        pdfRoot!
      )}
    </div>
  )
}

export default Hello
