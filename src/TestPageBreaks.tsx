import React, { useRef, useEffect } from 'react'
import html2pdf from 'html2pdf.js'
import PageBreakDemo from './PageBreakDemo'

const TestPageBreaks = () => {
  const testRef = useRef<HTMLDivElement>(null)

  // Function to calculate page dimensions in pixels (same as in Hello.tsx)
  const calculatePageDimensions = () => {
    const pageWidthMm = 216
    const pageHeightMm = 279
    const marginMm = 8.5
    
    const mmToPx = 96 / 25.4
    const usableHeightPx = (pageHeightMm - 2 * marginMm) * mmToPx
    const usableWidthPx = (pageWidthMm - 2 * marginMm) * mmToPx
    
    return { usableHeightPx, usableWidthPx }
  }

  // Function to handle page breaks (same as in Hello.tsx)
  const handlePageBreaks = (element: HTMLElement) => {
    const { usableHeightPx } = calculatePageDimensions()
    
    const sections = element.querySelectorAll(
      '.field-group, .field-group-col, .section-title, .dual-column'
    )
    
    let currentPageHeight = 0
    let pageNumber = 1
    
    const header = element.querySelector('.flex')
    if (header) {
      currentPageHeight += (header as HTMLElement).offsetHeight + 20
    }
    
    sections.forEach((section, index) => {
      const sectionElement = section as HTMLElement
      let sectionHeight = sectionElement.offsetHeight
      
      const hasImages = sectionElement.querySelector('img')
      if (hasImages) {
        sectionHeight += 20
      }
      
      if (
        currentPageHeight + sectionHeight > usableHeightPx &&
        currentPageHeight > 0
      ) {
        sectionElement.style.setProperty('break-before', 'page', 'important')
        sectionElement.classList.add('page-break-before')
        
        currentPageHeight = sectionHeight
        pageNumber++
        
        console.log(
          `Page break added before section ${index} (${sectionElement.className}), starting page ${pageNumber}`
        )
      } else {
        sectionElement.style.removeProperty('break-before')
        sectionElement.classList.remove('page-break-before')
        
        currentPageHeight += sectionHeight
      }
      
      const imageGroups = sectionElement.querySelectorAll('.grid-layout > div')
      imageGroups.forEach(group => {
        const groupElement = group as HTMLElement
        groupElement.classList.add('no-break')
        groupElement.style.setProperty('break-inside', 'avoid', 'important')
      })
      
      const images = sectionElement.querySelectorAll('img')
      images.forEach(img => {
        const imgElement = img as HTMLElement
        imgElement.style.setProperty('break-inside', 'avoid', 'important')
        imgElement.style.setProperty('page-break-inside', 'avoid', 'important')
      })
    })
    
    console.log(`Total pages estimated: ${pageNumber}`)
    console.log(`Usable page height: ${usableHeightPx}px`)
  }

  const generateTestPDF = async () => {
    if (!testRef.current) return

    console.log('Starting PDF generation test...')
    
    // Wait for images to load
    const images = Array.from(testRef.current.querySelectorAll('img'))
    await Promise.all(
      images.map(img => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve(img)
          } else {
            img.onload = () => resolve(img)
            img.onerror = () => resolve(img)
          }
        })
      })
    )

    // Apply page break logic
    handlePageBreaks(testRef.current)

    try {
      const pdfBlob = await html2pdf()
        .set({
          margin: 8.5,
          filename: 'page-break-test.pdf',
          image: { type: 'jpeg', quality: 1.0 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            imageTimeout: 0,
            allowTaint: true,
          },
          jsPDF: {
            unit: 'mm',
            format: 'letter',
            orientation: 'portrait',
          },
          pagebreak: {
            mode: ['css', 'avoid-all'],
            avoid: ['.no-break', '.field-group', '.field-group-col', '.grid-layout', 'img'],
            before: ['.page-break-before'],
          },
        })
        .from(testRef.current)
        .outputPdf('blob')

      // Create download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'page-break-test.pdf'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      console.log('PDF generated successfully!')
    } catch (error) {
      console.error('Error generating PDF:', error)
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px' }}>
        <h1>Page Break Feature Test</h1>
        <button 
          onClick={generateTestPDF}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Generate Test PDF
        </button>
        <p style={{ marginTop: '10px', color: '#666' }}>
          Click the button above to generate a PDF that demonstrates the page break feature.
          Check the browser console for page break information.
        </p>
      </div>
      
      <div 
        ref={testRef}
        style={{ 
          border: '1px solid #ccc', 
          padding: '20px',
          backgroundColor: 'white',
          maxWidth: '800px'
        }}
      >
        <PageBreakDemo />
      </div>
    </div>
  )
}

export default TestPageBreaks
