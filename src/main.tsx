import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './FormPDFLayout.tsx'
import Hello from './Hello.tsx'

const props = {
  imageBase64: null,
  formattedResponses: [
    {
      type: 'date',
      required: true,
      label: 'Date',
      className: 'form-control',
      name: 'date-1746903448645-0',
      subtype: 'date',
      order: 1,
      value: '2024-05-31',
    },
    {
      type: 'location',
      required: false,
      label: 'Location',
      name: 'location-1746903444753-0',
      value: 'No location information available',
      order: 2,
    },
    {
      type: 'autocomplete',
      required: true,
      label: 'Vehicle',
      className: 'form-control',
      name: 'autocomplete-1746903473665-0',
      requireValidOption: false,
      values: [
        {
          label: '1998 F150',
        },
        {
          label: '2000 F250',
        },
        {
          label: '2001 Toyota Tundra',
        },
        {
          label: '2005 Chevy 2500 HD',
        },
        {
          label: '2004 F150',
        },
        {
          label: '2018 F150',
        },
        {
          label: '2011 F150',
        },
        {
          label: '2016 GMC 2500 HD',
        },
        {
          label: '2013 F150',
        },
        {
          label: '2014 F150',
        },
        {
          label: 'Tesla Standard',
        },
        {
          label: 'Tesla Dual Motor',
        },
        {
          label: '2017 Promaster Van',
        },
        {
          label: 'Soccer Mom Van',
        },
      ],
      order: 3,
      value: 'Ullam laborum provid',
    },
    {
      type: 'header',
      subtype: 'h2',
      label: 'Interior Inspection',
      order: 4,
      value: '',
    },
    {
      type: 'number',
      required: true,
      label: 'Vehicle Mileage',
      className: 'form-control',
      name: 'number-1746903601789-0',
      subtype: 'number',
      order: 5,
      value: 504,
    },
    {
      type: 'file',
      required: true,
      label: 'Interior photos front & back seat',
      className: 'form-control',
      name: 'file-1746903633085-0',
      accept: 'image/*',
      multiple: true,
      order: 6,
      value: [
        {
          _id: '8bd1cad9-a31e-7cbf-5804-671a019c2f81',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_15_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746903633085-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Seat Belt',
      inline: true,
      name: 'radio-group-1746903683883-0',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 7,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Heater, Defroster, A/C',
      inline: true,
      name: 'radio-group-1746903751070',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 8,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Dash Warning Lights',
      inline: true,
      name: 'radio-group-1746903764028',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 9,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Horn',
      inline: true,
      name: 'radio-group-1746903781393',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 10,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Mirrors',
      inline: true,
      name: 'radio-group-1746903791125',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 11,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Window Cracks',
      inline: true,
      name: 'radio-group-1746903808100',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 12,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fire Extinguisher',
      inline: true,
      name: 'radio-group-1746903821272',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 13,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Cab Light',
      inline: true,
      name: 'radio-group-1746904178421',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 14,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wipers',
      inline: true,
      name: 'radio-group-1746904184759',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 15,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'First Aid Kit',
      inline: true,
      name: 'radio-group-1746904190725',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 16,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Door Locks',
      inline: true,
      name: 'radio-group-1746904227251',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 17,
      value: 'Pass',
    },
    {
      type: 'textarea',
      required: false,
      label: 'Notes on Interior Failed Items',
      className: 'form-control',
      name: 'textarea-1746904251900-0',
      subtype: 'textarea',
      order: 18,
      value: 'Optio qui mollit no',
    },
    {
      type: 'file',
      required: false,
      label: 'Photos of Interior Failed Items',
      className: 'form-control',
      name: 'file-1746905483702-0',
      accept: 'image/*',
      multiple: true,
      order: 19,
      value: [
        {
          _id: 'ae9c7f96-43a3-e125-ff74-fe8007459c6f',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_13_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905483702-0',
        },
      ],
    },
    {
      type: 'header',
      subtype: 'h3',
      label: 'Under the Hood Inspection',
      order: 20,
      value: '',
    },
    {
      type: 'file',
      required: true,
      label: 'Under the Hood Photo',
      className: 'form-control',
      name: 'file-1746905462069-0',
      accept: 'image/*',
      multiple: false,
      order: 21,
      value: [
        {
          _id: 'c6d5bf4c-4387-17d6-4263-dd74695ff53a',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_11_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905462069-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fan Belt (cracks, wear)',
      inline: true,
      name: 'radio-group-1746904236590',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 22,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Radiator Hoses (cracks, leaks)',
      inline: true,
      name: 'radio-group-1746904466368',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 23,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Battery (terminals, connections)',
      inline: true,
      name: 'radio-group-1746904483903',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 24,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Air Filter',
      inline: true,
      name: 'radio-group-1746904498379',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 25,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Transmission Fluid',
      inline: true,
      name: 'radio-group-1746904546199',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 26,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904569772-0',
      subtype: 'text',
      order: 27,
      value: 'Lorem ipsum',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Engine Oil',
      inline: true,
      name: 'radio-group-1746904565541',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 28,
      value: 'Full',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904681277',
      subtype: 'text',
      order: 29,
      value: 'Quod eos sunt quis r',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Fluid',
      inline: true,
      name: 'radio-group-1746904677141',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 30,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904600761',
      subtype: 'text',
      order: 31,
      value: 'Aut sapiente invento',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Windshield Wiper Fluid',
      inline: true,
      name: 'radio-group-1746904704547',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 32,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904848739',
      subtype: 'text',
      order: 33,
      value: 'Enim quam ad elit d',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Power Steering Fluid',
      inline: true,
      name: 'radio-group-1746904837041',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 34,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904848211',
      subtype: 'text',
      order: 35,
      value: 'Soluta ut elit sed ',
    },
    {
      type: 'header',
      subtype: 'h3',
      label: 'Exterior Inspection',
      order: 36,
      value: '',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Headlights & Brights',
      inline: true,
      name: 'radio-group-1746905001785',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 37,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Turn Signals',
      inline: true,
      name: 'radio-group-1746905030317',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 38,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Lights',
      inline: true,
      name: 'radio-group-1746905036549',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 39,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Reverse Lights',
      inline: true,
      name: 'radio-group-1746905044521',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 40,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Running Lights',
      inline: true,
      name: 'radio-group-1746905051499',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 41,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wheels - Lugs Tight',
      inline: true,
      name: 'radio-group-1746905055883',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 42,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Tire Pressure',
      inline: true,
      name: 'radio-group-1746905072018',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 43,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Tire Condition',
      inline: true,
      name: 'radio-group-1746905078394',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 44,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Trailer Hitch',
      inline: true,
      name: 'radio-group-1746905086194',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 45,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Body Damage',
      inline: true,
      name: 'radio-group-1746905148121',
      other: false,
      values: [
        {
          label: 'New',
        },
        {
          label: 'Existing',
        },
        {
          label: 'None',
        },
      ],
      order: 46,
      value: 'None',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fluid Leaks Under Vehicle',
      inline: true,
      name: 'radio-group-1746905217874',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 47,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Pads',
      inline: true,
      name: 'radio-group-1746905238018',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 48,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Shock Absorbers',
      inline: true,
      name: 'radio-group-1746905243914',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 49,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'U-Joints on Drive Shafts',
      inline: true,
      name: 'radio-group-1746905250563',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 50,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Logos & Lettering',
      inline: true,
      name: 'radio-group-1746905269038',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 51,
      value: 'Pass',
    },
    {
      type: 'file',
      required: true,
      label: 'Photos of all 4 sides',
      className: 'form-control',
      name: 'file-1746905400838-0',
      accept: 'image/*',
      multiple: true,
      order: 52,
      value: [
        {
          _id: '52b99336-cb59-ae4c-370f-d8d7fba798c8',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_13_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905400838-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wash Outside',
      inline: true,
      name: 'radio-group-1746905281278',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 53,
      value: 'Pass',
    },
    {
      type: 'textarea',
      required: false,
      label: 'Notes on Exterior Failed Items',
      className: 'form-control',
      name: 'textarea-1746905325434-0',
      subtype: 'textarea',
      order: 54,
      value: 'Quia dignissimos dol',
    },
    {
      type: 'file',
      required: false,
      label: 'Pictures of Failed Exterior Items',
      className: 'form-control',
      name: 'file-1746905326160-0',
      accept: 'image/*',
      multiple: true,
      order: 55,
      value: [
        {
          _id: '8f9db256-23a2-d727-1cfa-c881275f8f92',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_6_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '40b189eb-704e-f0c0-57a1-329256dbc056',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_10_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '34cb45a1-1cad-6ce7-afd5-636c90d1d2ee',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '234d80ed-bccd-f651-2ebf-15fd98f7e6a3',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_9_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '58956719-7c5c-0835-edbc-df95fe6aa396',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_15_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '436f4901-8b56-b60a-05c8-73bec234bbea',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_7_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'b19c96ff-e626-3778-6a35-f6669c4ed655',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '7b0de5fc-a267-8fd2-aa23-c76f2762e8bd',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_2_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'd3072884-7c34-43bf-b456-874684fa3fb3',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_5_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'a2220092-e031-08f6-fbbd-221757703d2d',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_3_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '6c670688-4759-b084-ad41-a08c79d96f07',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_8_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'd18276c0-76bb-2fc9-537f-bd0686f808f1',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_4_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '68be9380-37a1-3c30-19f7-6807c23fa7d0',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_16_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '058b0f8b-ee42-390e-d29c-ae4c61004c68',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_6_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'c3d5537f-8d92-b689-3e12-e1d93f9b4cdf',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'e4474ce1-bb7b-05b3-65f8-130aa2633132',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_5_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'e5b0048b-4237-2951-2348-59fbae0d449a',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_3_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'c0f2a5e3-0b3c-434b-253a-9505283f11be',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_11_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'f3a3e597-727a-33c7-4180-2b5a9782cd66',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_7_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'fc22ba22-df35-db83-f46e-60ce59f047f4',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '694918ce-0e65-07e4-3edd-08e62941ea3f',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_8_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '025c4216-499f-96ec-2608-79aa104a6ce9',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_2_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'd4ca3297-33e5-de03-970f-8948e0c1baf6',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_4_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'bd22201f-c67e-8aee-d597-7bc14d51e740',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_12_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'cefc97b1-6cdb-c0ff-c9df-1b13acf18bd7',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_9_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'ebb9b99a-93c6-ddc7-9a03-5c749b4c81d3',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_10_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '089ddc08-0654-0fa8-73eb-ed34582f9d6a',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_13_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '37341bd3-24a1-0e30-7e80-63f5a5d401a8',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755929659245-file_example_JPG_100kB__Copy_14_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
      ],
    },
    {
      type: 'file',
      required: false,
      label: 'Video of Failed Item (if needed)',
      className: 'form-control',
      name: 'file-1746905557206-0',
      accept: 'video/*',
      multiple: true,
      order: 56,
      value: [],
    },
  ],
  name: 'Vehicle Inspection Form',
  createdBy: 'vipin yadav',
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {/* <App {...props} /> */}
    <Hello {...props} />
  </StrictMode>
)
