# PDF Page Break Feature

This feature automatically prevents content from being split across pages when generating PDFs using html2pdf.js with letter-sized pages.

## How It Works

The system calculates the available space on each page and automatically inserts page breaks before content sections that would overflow to the next page. This ensures that:

- Field groups stay together
- Image sections are not split
- Headers and sections maintain proper spacing
- Content flows naturally across pages

## Key Features

### 1. Automatic Page Break Calculation
- Calculates usable page height based on letter size (8.5" x 11") with 8.5mm margins
- Accounts for header height and content spacing
- Adds page breaks when content would overflow

### 2. Content Protection
- Protects field groups from being split
- Keeps image groups together
- Prevents headers from being orphaned
- Maintains section integrity

### 3. Smart Image Handling
- Groups of images are kept together
- Individual images are protected from splitting
- Extra buffer space added for image sections

## Configuration

### PDF Settings
```javascript
const pdfConfig = {
  margin: 8.5,
  filename: 'inspection-report.pdf',
  image: { type: 'jpeg', quality: 1.0 },
  html2canvas: {
    scale: 2,
    useCORS: true,
    imageTimeout: 0,
    allowTaint: true,
  },
  jsPDF: {
    unit: 'mm',
    format: 'letter',
    orientation: 'portrait',
  },
  pagebreak: {
    mode: ['css', 'avoid-all'],
    avoid: ['.no-break', '.field-group', '.field-group-col', '.grid-layout', 'img'],
    before: ['.page-break-before'],
  },
}
```

### CSS Classes Used

#### Protection Classes
- `.no-break` - Prevents content from being split
- `.field-group` - Protects field groups
- `.field-group-col` - Protects column field groups
- `.section-title` - Protects section headers
- `.grid-layout` - Protects image grids

#### Page Break Classes
- `.page-break-before` - Forces a page break before the element
- `.page-break-after` - Forces a page break after the element

## Usage

### Basic Implementation
The page break logic is automatically applied when generating PDFs. The main function `handlePageBreaks()` is called before PDF generation:

```javascript
// Apply page break logic
handlePageBreaks(element)

// Generate PDF
const pdfBlob = await html2pdf().set(config).from(element).outputPdf('blob')
```

### Debug Mode
To visualize page breaks during development, uncomment this line in the code:

```javascript
// addPageBreakIndicators(element)
```

This will add red visual indicators showing where page breaks will occur.

### Custom Content Protection
To protect custom content from being split, add the `no-break` class:

```html
<div className="no-break">
  <!-- This content will not be split across pages -->
</div>
```

## Technical Details

### Page Dimensions
- **Page Size**: Letter (8.5" x 11" / 216mm x 279mm)
- **Margins**: 8.5mm on all sides
- **Usable Height**: ~1000px (calculated at 96 DPI)

### Height Calculation
The system calculates content height including:
- Header section height
- Individual section heights
- Extra buffer for image sections
- Padding and margins

### Break Logic
1. Iterate through all content sections
2. Calculate cumulative height
3. Check if adding next section would overflow
4. If overflow detected, add page break before section
5. Reset height counter for new page

## Troubleshooting

### Content Still Splitting
- Ensure proper CSS classes are applied
- Check if content height exceeds single page capacity
- Verify html2canvas scale settings

### Images Not Loading
- Ensure CORS is properly configured
- Check image URLs are accessible
- Verify `useCORS: true` in html2canvas settings

### Page Breaks Too Aggressive
- Adjust the height buffer in `handlePageBreaks()`
- Modify the usable height calculation
- Fine-tune margin settings

## Example Usage

See `PageBreakDemo.tsx` for a complete example of how the page break feature works with various content types including text fields, headers, and image groups.

The demo generates test content that demonstrates:
- Multiple field groups
- Section headers
- Image galleries
- Mixed content types

This helps visualize how content flows across pages and where breaks are automatically inserted.
